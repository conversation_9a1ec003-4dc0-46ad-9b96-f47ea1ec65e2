#!/usr/bin/env python3
"""
Visualization script for DynamicGaussianReadoutEI class.
"""

import torch
import matplotlib.pyplot as plt
import numpy as np
import sys
import os

# Add the DataYatesV1 directory to the path
sys.path.append('DataYatesV1')

from models.modules.readout import DynamicGaussianReadoutEI

def visualize_ei_gaussians():
    """Visualize the excitatory and inhibitory Gaussian masks."""
    print("Creating visualization of excitatory and inhibitory Gaussians...")
    
    # Create readout with different std for ex vs inh
    readout = DynamicGaussianReadoutEI(
        in_channels=16, 
        n_units=3,
        initial_std_ex=2.0,  # Smaller (more focused)
        initial_std_inh=5.0  # Larger (more spread out)
    )
    
    # Create dummy input to trigger grid caching
    x = torch.randn(1, 16, 20, 20)
    _ = readout(x)  # This caches the grid
    
    # Get spatial weights
    spatial_weights = readout.get_spatial_weights()
    ex_masks = spatial_weights['excitatory'].detach().cpu().numpy()
    inh_masks = spatial_weights['inhibitory'].detach().cpu().numpy()
    
    # Create visualization
    n_units = readout.n_units
    fig, axes = plt.subplots(2, n_units, figsize=(n_units * 3, 6))
    
    if n_units == 1:
        axes = axes.reshape(2, 1)
    
    for i in range(n_units):
        # Excitatory masks (top row)
        im_ex = axes[0, i].imshow(ex_masks[i], cmap='Reds', interpolation='bilinear')
        axes[0, i].set_title(f'Excitatory Unit {i}')
        axes[0, i].axis('off')
        plt.colorbar(im_ex, ax=axes[0, i], fraction=0.046, pad=0.04)
        
        # Inhibitory masks (bottom row)
        im_inh = axes[1, i].imshow(inh_masks[i], cmap='Blues', interpolation='bilinear')
        axes[1, i].set_title(f'Inhibitory Unit {i}')
        axes[1, i].axis('off')
        plt.colorbar(im_inh, ax=axes[1, i], fraction=0.046, pad=0.04)
    
    plt.suptitle('Excitatory (Red) vs Inhibitory (Blue) Gaussian Masks', fontsize=14)
    plt.tight_layout()
    plt.savefig('ei_gaussians.png', dpi=150, bbox_inches='tight')
    print("Saved visualization to 'ei_gaussians.png'")
    
    # Print some statistics
    print(f"\nStatistics:")
    print(f"Excitatory std parameters: {readout.std_ex.detach().cpu().numpy()}")
    print(f"Inhibitory std parameters: {readout.std_inh.detach().cpu().numpy()}")
    print(f"Excitatory mean parameters: {readout.mean_ex.detach().cpu().numpy()}")
    print(f"Inhibitory mean parameters: {readout.mean_inh.detach().cpu().numpy()}")
    
    return fig

def compare_with_original():
    """Compare EI version with original DynamicGaussianReadout."""
    print("\nComparing EI version with original DynamicGaussianReadout...")
    
    from models.modules.readout import DynamicGaussianReadout
    
    # Create both readouts
    readout_original = DynamicGaussianReadout(in_channels=16, n_units=2, initial_std=4.0)
    readout_ei = DynamicGaussianReadoutEI(in_channels=16, n_units=2, 
                                         initial_std_ex=3.0, initial_std_inh=5.0)
    
    # Same input
    x = torch.randn(1, 16, 16, 16)
    
    # Forward passes
    out_original = readout_original(x)
    out_ei = readout_ei(x)
    
    print(f"Original readout output: {out_original}")
    print(f"EI readout output: {out_ei}")
    print(f"Output shapes - Original: {out_original.shape}, EI: {out_ei.shape}")
    
    # Compare parameter counts
    original_params = sum(p.numel() for p in readout_original.parameters())
    ei_params = sum(p.numel() for p in readout_ei.parameters())
    
    print(f"Parameter count - Original: {original_params}, EI: {ei_params}")
    print(f"EI has {ei_params - original_params} more parameters")

def test_weight_constraint_effects():
    """Test how different weight constraints affect the output."""
    print("\nTesting weight constraint effects...")
    
    # Create readouts with different constraints
    readout_relu = DynamicGaussianReadoutEI(16, 2, weight_constraint_fn=torch.relu)
    readout_softplus = DynamicGaussianReadoutEI(16, 2, weight_constraint_fn=torch.nn.functional.softplus)
    
    # Set some negative hidden weights
    with torch.no_grad():
        readout_relu._features_ex_weight.data.normal_(mean=-0.5, std=1.0)
        readout_softplus._features_ex_weight.data.copy_(readout_relu._features_ex_weight.data)
        
        readout_relu._features_inh_weight.data.normal_(mean=-0.5, std=1.0)
        readout_softplus._features_inh_weight.data.copy_(readout_relu._features_inh_weight.data)
    
    # Same input
    x = torch.randn(1, 16, 12, 12)
    
    out_relu = readout_relu(x)
    out_softplus = readout_softplus(x)
    
    print(f"ReLU constraint output: {out_relu}")
    print(f"Softplus constraint output: {out_softplus}")
    
    # Check weight statistics
    relu_ex_weights = readout_relu.features_ex_weight
    softplus_ex_weights = readout_softplus.features_ex_weight
    
    print(f"ReLU weights - min: {relu_ex_weights.min():.6f}, max: {relu_ex_weights.max():.6f}")
    print(f"Softplus weights - min: {softplus_ex_weights.min():.6f}, max: {softplus_ex_weights.max():.6f}")

if __name__ == "__main__":
    print("Running DynamicGaussianReadoutEI visualization and comparison...\n")
    
    try:
        fig = visualize_ei_gaussians()
        compare_with_original()
        test_weight_constraint_effects()
        
        print("\n✓ Visualization and comparison completed successfully!")
        print("Check 'ei_gaussians.png' for the Gaussian mask visualization.")
        
    except Exception as e:
        print(f"\n❌ Visualization failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
