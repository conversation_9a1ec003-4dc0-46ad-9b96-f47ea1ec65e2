#!/usr/bin/env python3
"""
Test script for DynamicGaussianReadoutEI class.
"""

import torch
import torch.nn.functional as F
import sys
import os

# Add the DataYatesV1 directory to the path
sys.path.append('DataYatesV1')

from models.modules.readout import DynamicGaussianReadoutEI

def test_basic_functionality():
    """Test basic functionality of DynamicGaussianReadoutEI."""
    print("Testing DynamicGaussianReadoutEI basic functionality...")
    
    # Create a simple test case
    in_channels = 32
    n_units = 5
    batch_size = 2
    H, W = 16, 16
    
    # Initialize the readout
    readout = DynamicGaussianReadoutEI(
        in_channels=in_channels, 
        n_units=n_units, 
        bias=True,
        initial_std_ex=3.0,
        initial_std_inh=6.0
    )
    
    print(f"Created readout with {in_channels} input channels, {n_units} units")
    print(f"Excitatory std initialized to 3.0, inhibitory std initialized to 6.0")
    
    # Create test input
    x = torch.randn(batch_size, in_channels, H, W)
    print(f"Input shape: {x.shape}")
    
    # Forward pass
    output = readout(x)
    print(f"Output shape: {output.shape}")
    print(f"Expected output shape: ({batch_size}, {n_units})")
    
    assert output.shape == (batch_size, n_units), f"Output shape mismatch: {output.shape} vs ({batch_size}, {n_units})"
    
    # Test that weights are positive
    ex_weights = readout.features_ex_weight
    inh_weights = readout.features_inh_weight
    
    print(f"Excitatory weights min: {ex_weights.min().item():.6f}")
    print(f"Inhibitory weights min: {inh_weights.min().item():.6f}")
    
    assert ex_weights.min() >= 0, "Excitatory weights should be non-negative"
    assert inh_weights.min() >= 0, "Inhibitory weights should be non-negative"
    
    # Test spatial weights
    spatial_weights = readout.get_spatial_weights()
    print(f"Spatial weights keys: {spatial_weights.keys()}")
    print(f"Excitatory spatial weights shape: {spatial_weights['excitatory'].shape}")
    print(f"Inhibitory spatial weights shape: {spatial_weights['inhibitory'].shape}")
    
    assert spatial_weights['excitatory'].shape == (n_units, H, W)
    assert spatial_weights['inhibitory'].shape == (n_units, H, W)
    
    print("✓ Basic functionality test passed!")
    return readout, x, output

def test_weight_constraints():
    """Test different weight constraint functions."""
    print("\nTesting different weight constraint functions...")
    
    in_channels = 16
    n_units = 3
    
    # Test with ReLU (default)
    readout_relu = DynamicGaussianReadoutEI(in_channels, n_units, weight_constraint_fn=torch.relu)
    
    # Test with softplus
    readout_softplus = DynamicGaussianReadoutEI(in_channels, n_units, weight_constraint_fn=F.softplus)
    
    # Test with square (lambda function)
    readout_square = DynamicGaussianReadoutEI(in_channels, n_units, weight_constraint_fn=lambda x: x**2)
    
    # Set some negative values in the hidden weights to test constraints
    with torch.no_grad():
        readout_relu._features_ex_weight.data.fill_(-1.0)
        readout_softplus._features_ex_weight.data.fill_(-1.0)
        readout_square._features_ex_weight.data.fill_(-1.0)
    
    # Check that constraints work
    relu_weights = readout_relu.features_ex_weight
    softplus_weights = readout_softplus.features_ex_weight
    square_weights = readout_square.features_ex_weight
    
    print(f"ReLU constraint on -1.0: {relu_weights[0,0,0,0].item():.6f}")
    print(f"Softplus constraint on -1.0: {softplus_weights[0,0,0,0].item():.6f}")
    print(f"Square constraint on -1.0: {square_weights[0,0,0,0].item():.6f}")
    
    assert relu_weights.min() >= 0, "ReLU constraint failed"
    assert softplus_weights.min() >= 0, "Softplus constraint failed"
    assert square_weights.min() >= 0, "Square constraint failed"
    
    print("✓ Weight constraint test passed!")

def test_5d_input():
    """Test with 5D input (sequence dimension)."""
    print("\nTesting 5D input handling...")
    
    readout = DynamicGaussianReadoutEI(in_channels=16, n_units=3)
    
    # Create 5D input (N, C, S, H, W)
    x_5d = torch.randn(2, 16, 10, 8, 8)  # batch=2, channels=16, sequence=10, H=8, W=8
    
    output = readout(x_5d)
    print(f"5D input shape: {x_5d.shape}")
    print(f"Output shape: {output.shape}")
    
    assert output.shape == (2, 3), f"Output shape mismatch for 5D input: {output.shape}"
    
    print("✓ 5D input test passed!")

def test_excitatory_inhibitory_difference():
    """Test that excitatory and inhibitory pathways produce different results."""
    print("\nTesting excitatory vs inhibitory pathway differences...")
    
    readout = DynamicGaussianReadoutEI(in_channels=8, n_units=2)
    x = torch.randn(1, 8, 12, 12)
    
    # Get the individual components
    with torch.no_grad():
        # Manually compute excitatory and inhibitory components
        feat_ex = F.conv2d(x, readout.features_ex_weight, bias=None)
        feat_inh = F.conv2d(x, readout.features_inh_weight, bias=None)
        
        mask_ex = readout.compute_gaussian_mask(12, 12, x.device, pathway='ex')
        mask_inh = readout.compute_gaussian_mask(12, 12, x.device, pathway='inh')
        
        out_ex = (feat_ex * mask_ex.unsqueeze(0)).sum(dim=(-2, -1))
        out_inh = (feat_inh * mask_inh.unsqueeze(0)).sum(dim=(-2, -1))
        
        manual_output = out_ex - out_inh
        if readout.bias is not None:
            manual_output = manual_output + readout.bias
    
    # Compare with forward pass
    forward_output = readout(x)
    
    print(f"Manual computation output: {manual_output}")
    print(f"Forward pass output: {forward_output}")
    print(f"Difference: {torch.abs(manual_output - forward_output).max().item():.8f}")
    
    assert torch.allclose(manual_output, forward_output, atol=1e-6), "Manual and forward pass outputs don't match"
    
    # Check that excitatory and inhibitory are different
    ex_std_mean = readout.std_ex.mean().item()
    inh_std_mean = readout.std_inh.mean().item()
    
    print(f"Average excitatory std: {ex_std_mean:.3f}")
    print(f"Average inhibitory std: {inh_std_mean:.3f}")
    
    # Since we initialized with different stds, they should be different
    assert abs(ex_std_mean - inh_std_mean) > 1.0, "Excitatory and inhibitory stds should be different"
    
    print("✓ Excitatory vs inhibitory test passed!")

if __name__ == "__main__":
    print("Running DynamicGaussianReadoutEI tests...\n")
    
    try:
        test_basic_functionality()
        test_weight_constraints()
        test_5d_input()
        test_excitatory_inhibitory_difference()
        
        print("\n🎉 All tests passed! DynamicGaussianReadoutEI is working correctly.")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
